{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Portfolio.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  const portfolioItems = [{\n    href: \"https://threed-e-commerce.onrender.com\",\n    image: \"/3D E-Comm.PNG\",\n    alt: \"3D Ecommerce\",\n    title: \"3D Ecommerce\"\n  }, {\n    href: \"#\",\n    image: \"/ex1.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex2.png\",\n    alt: \"Nexit Brand Identity\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex3.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex4.1.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex5.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/bussniss web UI.PNG\",\n    alt: \"Business Web UI\",\n    title: \"Available in git Will be deployed soon.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"portfolio\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: [\"Top Projects\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"discover-button\",\n      onClick: () => console.log('Discover more clicked'),\n      children: \"DISCOVER MORE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-carousel\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-track infinite-carousel\",\n        children: [...portfolioItems, ...portfolioItems].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"portfolio-item\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.image,\n              alt: item.alt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Portfolio", "portfolioItems", "href", "image", "alt", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "console", "log", "map", "item", "index", "target", "rel", "src", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Portfolio.js"], "sourcesContent": ["import React from 'react';\n\nconst Portfolio = () => {\n  const portfolioItems = [\n    {\n      href: \"https://threed-e-commerce.onrender.com\",\n      image: \"/3D E-Comm.PNG\",\n      alt: \"3D Ecommerce\",\n      title: \"3D Ecommerce\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex1.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex2.png\",\n      alt: \"Nexit Brand Identity\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex3.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex4.1.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex5.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/bussniss web UI.PNG\",\n      alt: \"Business Web UI\",\n      title: \"Available in git Will be deployed soon.\"\n    }\n  ];\n\n  return (\n    <section className=\"portfolio\">\n      <h2>Top Projects<br /></h2>\n      <button className=\"discover-button\" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>\n      <div className=\"portfolio-carousel\">\n        <div className=\"carousel-track infinite-carousel\">\n          {/* Render items twice for infinite scroll */}\n          {[...portfolioItems, ...portfolioItems].map((item, index) => (\n            <div key={index} className=\"portfolio-item\">\n              <a href={item.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                <img src={item.image} alt={item.alt} />\n                <p>{item.title}</p>\n              </a>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,cAAc,GAAG,CACrB;IACEC,IAAI,EAAE,wCAAwC;IAC9CC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,YAAY;IACnBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,sBAAsB;IAC7BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAC5BR,OAAA;MAAAQ,QAAA,GAAI,cAAY,eAAAR,OAAA;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3BZ,OAAA;MAAQO,SAAS,EAAC,iBAAiB;MAACM,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE;MAAAP,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC/GZ,OAAA;MAAKO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCR,OAAA;QAAKO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAE9C,CAAC,GAAGN,cAAc,EAAE,GAAGA,cAAc,CAAC,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtDlB,OAAA;UAAiBO,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eACzCR,OAAA;YAAGG,IAAI,EAAEc,IAAI,CAACd,IAAK;YAACgB,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAZ,QAAA,gBAC3DR,OAAA;cAAKqB,GAAG,EAAEJ,IAAI,CAACb,KAAM;cAACC,GAAG,EAAEY,IAAI,CAACZ;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCZ,OAAA;cAAAQ,QAAA,EAAIS,IAAI,CAACX;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GAJIM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACU,EAAA,GAjEIrB,SAAS;AAmEf,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}