import React, { useState, useRef } from 'react';
import { Link } from 'react-router-dom';

const MODAL_TIMEOUT = 1200; // ms to reset click count

const Header = () => {
  const [showAdminModal, setShowAdminModal] = useState(false);
  const [clickCount, setClickCount] = useState(0);
  const clickTimer = useRef(null);

  const handleLogoClick = (e) => {
    e.preventDefault();
    setClickCount(prev => {
      const newCount = prev + 1;
      if (clickTimer.current) clearTimeout(clickTimer.current);
      if (newCount >= 5) {
        setShowAdminModal(true);
        setTimeout(() => setClickCount(0), 100); // reset after modal
        return 0;
      } else {
        clickTimer.current = setTimeout(() => setClickCount(0), MODAL_TIMEOUT);
        return newCount;
      }
    });
    // No reload or navigation on single click
  };

  return (
    <header>
      <div className="logo">
        <a href="/" onClick={handleLogoClick}>
          <img src="/logo.PNG" alt="Logo" className="logo-img" />
        </a>
      </div>
      <a 
        href="https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93" 
        className="cv-button"
        target="_blank"
        rel="noopener noreferrer"
      >
        Get CV
      </a>
      {showAdminModal && (
        <div className="admin-modal-overlay" onClick={() => setShowAdminModal(false)}>
          <div className="admin-modal" onClick={e => e.stopPropagation()}>
            <button className="admin-modal-close" onClick={() => setShowAdminModal(false)}>&times;</button>
            <h2 className="admin-modal-title">Admin Login</h2>
            <form className="admin-modal-form">
              <label htmlFor="admin-email">Email</label>
              <input type="email" id="admin-email" name="email" autoComplete="username" required />
              <label htmlFor="admin-password">Password</label>
              <input type="password" id="admin-password" name="password" autoComplete="current-password" required />
              <button type="submit" className="admin-modal-submit">Login</button>
            </form>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
