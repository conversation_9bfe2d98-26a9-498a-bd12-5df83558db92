{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Portfolio.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  _s();\n  const carouselTrackRef = useRef(null);\n  useEffect(() => {\n    const carouselTrack = carouselTrackRef.current;\n    if (!carouselTrack) return;\n    let isDragging = false;\n    let startX;\n    let startScrollLeft;\n    let autoScrollInterval;\n    let scrollSpeed = 1;\n    let isMobile = window.innerWidth <= 768;\n\n    // Auto-scroll function - continuous movement without stopping\n    const startAutoScroll = () => {\n      if (autoScrollInterval) clearInterval(autoScrollInterval);\n      autoScrollInterval = setInterval(() => {\n        // Always scroll continuously, regardless of user interaction\n        carouselTrack.scrollLeft += scrollSpeed;\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        }\n      }, 16);\n    };\n    const stopAutoScroll = () => {\n      if (autoScrollInterval) {\n        clearInterval(autoScrollInterval);\n        autoScrollInterval = null;\n      }\n    };\n\n    // Mouse events\n    const handleMouseDown = e => {\n      isDragging = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n    const handleMouseMove = e => {\n      if (!isDragging) return;\n      const x = e.pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n    const handleMouseUp = () => {\n      if (isDragging) {\n        isDragging = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n    const handleWheel = e => {\n      e.preventDefault();\n      const wheelDelta = e.deltaY;\n      const scrollAmount = wheelDelta > 0 ? 50 : -50;\n      carouselTrack.scrollLeft += scrollAmount;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    // Touch event handlers\n    const handleTouchStart = e => {\n      isDragging = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.touches[0].pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n    const handleTouchMove = e => {\n      if (!isDragging) return;\n      // For mobile, let the browser handle native scrolling\n      if (isMobile) {\n        // Just track the movement, let native scrolling work\n        return;\n      }\n\n      // For tablets and larger screens, use custom scrolling\n      const x = e.touches[0].pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n    const handleTouchEnd = () => {\n      if (isDragging) {\n        lastTouchTime = Date.now();\n        isDragging = false;\n        isHovering = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n\n    // Handle window resize for mobile detection\n    const handleResize = () => {\n      isMobile = window.innerWidth <= 768;\n    };\n\n    // Handle scroll for mobile infinite loop\n    const handleScroll = () => {\n      if (isMobile) {\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        } else if (carouselTrack.scrollLeft < 0) {\n          carouselTrack.scrollLeft = trackWidth - 1;\n        }\n      }\n    };\n\n    // Add event listeners\n    carouselTrack.addEventListener('mouseenter', handleMouseEnter);\n    carouselTrack.addEventListener('mouseleave', handleMouseLeave);\n    carouselTrack.addEventListener('mousedown', handleMouseDown);\n    carouselTrack.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    carouselTrack.addEventListener('wheel', handleWheel, {\n      passive: false\n    });\n\n    // Touch event listeners\n    carouselTrack.addEventListener('touchstart', handleTouchStart, {\n      passive: true\n    });\n    carouselTrack.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    carouselTrack.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n\n    // Additional event listeners\n    window.addEventListener('resize', handleResize);\n    carouselTrack.addEventListener('scroll', handleScroll);\n\n    // Start auto-scroll\n    startAutoScroll();\n\n    // Cleanup\n    return () => {\n      stopAutoScroll();\n      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);\n      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);\n      carouselTrack.removeEventListener('mousedown', handleMouseDown);\n      carouselTrack.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      carouselTrack.removeEventListener('wheel', handleWheel);\n\n      // Remove touch event listeners\n      carouselTrack.removeEventListener('touchstart', handleTouchStart);\n      carouselTrack.removeEventListener('touchmove', handleTouchMove);\n      carouselTrack.removeEventListener('touchend', handleTouchEnd);\n\n      // Remove additional event listeners\n      window.removeEventListener('resize', handleResize);\n      carouselTrack.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n  const portfolioItems = [{\n    href: \"https://threed-e-commerce.onrender.com\",\n    image: \"/3D E-Comm.PNG\",\n    alt: \"3D Ecommerce\",\n    title: \"3D Ecommerce\"\n  }, {\n    href: \"#\",\n    image: \"/ex1.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex2.png\",\n    alt: \"Nexit Brand Identity\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex3.webp\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex4.1.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/ex5.png\",\n    alt: \"Yalla Go Posters\",\n    title: \"Will be deployed soon.\"\n  }, {\n    href: \"#\",\n    image: \"/bussniss web UI.PNG\",\n    alt: \"Business Web UI\",\n    title: \"Available in git Will be deployed soon.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"portfolio\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: [\"Top Projects\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"discover-button\",\n      onClick: () => console.log('Discover more clicked'),\n      children: \"DISCOVER MORE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-carousel\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-track\",\n        ref: carouselTrackRef,\n        children: [...portfolioItems, ...portfolioItems].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"portfolio-item\",\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.image,\n              alt: item.alt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n};\n_s(Portfolio, \"szS5YaQSbEP6Gfx9ah0FKeYvPZs=\");\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Portfolio", "_s", "carouselTrackRef", "carouselTrack", "current", "isDragging", "startX", "startScrollLeft", "autoScrollInterval", "scrollSpeed", "isMobile", "window", "innerWidth", "startAutoScroll", "clearInterval", "setInterval", "scrollLeft", "trackWidth", "scrollWidth", "stopAutoScroll", "handleMouseDown", "e", "classList", "add", "pageX", "handleMouseMove", "x", "walk", "handleMouseUp", "remove", "handleWheel", "preventDefault", "wheelDelta", "deltaY", "scrollAmount", "handleTouchStart", "touches", "handleTouchMove", "handleTouchEnd", "lastTouchTime", "Date", "now", "isHovering", "handleResize", "handleScroll", "addEventListener", "handleMouseEnter", "handleMouseLeave", "document", "passive", "removeEventListener", "portfolioItems", "href", "image", "alt", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "console", "log", "ref", "map", "item", "index", "target", "rel", "src", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Portfolio.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst Portfolio = () => {\n  const carouselTrackRef = useRef(null);\n\n  useEffect(() => {\n    const carouselTrack = carouselTrackRef.current;\n    if (!carouselTrack) return;\n\n    let isDragging = false;\n    let startX;\n    let startScrollLeft;\n    let autoScrollInterval;\n    let scrollSpeed = 1;\n    let isMobile = window.innerWidth <= 768;\n\n    // Auto-scroll function - continuous movement without stopping\n    const startAutoScroll = () => {\n      if (autoScrollInterval) clearInterval(autoScrollInterval);\n      autoScrollInterval = setInterval(() => {\n        // Always scroll continuously, regardless of user interaction\n        carouselTrack.scrollLeft += scrollSpeed;\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        }\n      }, 16);\n    };\n\n    const stopAutoScroll = () => {\n      if (autoScrollInterval) {\n        clearInterval(autoScrollInterval);\n        autoScrollInterval = null;\n      }\n    };\n\n    // Mouse events\n    const handleMouseDown = (e) => {\n      isDragging = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n\n    const handleMouseMove = (e) => {\n      if (!isDragging) return;\n      const x = e.pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    const handleMouseUp = () => {\n      if (isDragging) {\n        isDragging = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n\n    const handleWheel = (e) => {\n      e.preventDefault();\n      const wheelDelta = e.deltaY;\n      const scrollAmount = wheelDelta > 0 ? 50 : -50;\n      carouselTrack.scrollLeft += scrollAmount;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    // Touch event handlers\n    const handleTouchStart = (e) => {\n      isDragging = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.touches[0].pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n\n    const handleTouchMove = (e) => {\n      if (!isDragging) return;\n      // For mobile, let the browser handle native scrolling\n      if (isMobile) {\n        // Just track the movement, let native scrolling work\n        return;\n      }\n\n      // For tablets and larger screens, use custom scrolling\n      const x = e.touches[0].pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    const handleTouchEnd = () => {\n      if (isDragging) {\n        lastTouchTime = Date.now();\n        isDragging = false;\n        isHovering = false;\n        carouselTrack.classList.remove('dragging');\n      }\n    };\n\n    // Handle window resize for mobile detection\n    const handleResize = () => {\n      isMobile = window.innerWidth <= 768;\n    };\n\n    // Handle scroll for mobile infinite loop\n    const handleScroll = () => {\n      if (isMobile) {\n        const trackWidth = carouselTrack.scrollWidth / 2;\n        if (carouselTrack.scrollLeft >= trackWidth) {\n          carouselTrack.scrollLeft = 0;\n        } else if (carouselTrack.scrollLeft < 0) {\n          carouselTrack.scrollLeft = trackWidth - 1;\n        }\n      }\n    };\n\n    // Add event listeners\n    carouselTrack.addEventListener('mouseenter', handleMouseEnter);\n    carouselTrack.addEventListener('mouseleave', handleMouseLeave);\n    carouselTrack.addEventListener('mousedown', handleMouseDown);\n    carouselTrack.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    carouselTrack.addEventListener('wheel', handleWheel, { passive: false });\n\n    // Touch event listeners\n    carouselTrack.addEventListener('touchstart', handleTouchStart, { passive: true });\n    carouselTrack.addEventListener('touchmove', handleTouchMove, { passive: true });\n    carouselTrack.addEventListener('touchend', handleTouchEnd, { passive: true });\n\n    // Additional event listeners\n    window.addEventListener('resize', handleResize);\n    carouselTrack.addEventListener('scroll', handleScroll);\n\n    // Start auto-scroll\n    startAutoScroll();\n\n    // Cleanup\n    return () => {\n      stopAutoScroll();\n      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);\n      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);\n      carouselTrack.removeEventListener('mousedown', handleMouseDown);\n      carouselTrack.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      carouselTrack.removeEventListener('wheel', handleWheel);\n\n      // Remove touch event listeners\n      carouselTrack.removeEventListener('touchstart', handleTouchStart);\n      carouselTrack.removeEventListener('touchmove', handleTouchMove);\n      carouselTrack.removeEventListener('touchend', handleTouchEnd);\n\n      // Remove additional event listeners\n      window.removeEventListener('resize', handleResize);\n      carouselTrack.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n\n  const portfolioItems = [\n    {\n      href: \"https://threed-e-commerce.onrender.com\",\n      image: \"/3D E-Comm.PNG\",\n      alt: \"3D Ecommerce\",\n      title: \"3D Ecommerce\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex1.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex2.png\",\n      alt: \"Nexit Brand Identity\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex3.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex4.1.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex5.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/bussniss web UI.PNG\",\n      alt: \"Business Web UI\",\n      title: \"Available in git Will be deployed soon.\"\n    }\n  ];\n\n  return (\n    <section className=\"portfolio\">\n      <h2>Top Projects<br /></h2>\n      <button className=\"discover-button\" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>\n      <div className=\"portfolio-carousel\">\n        <div className=\"carousel-track\" ref={carouselTrackRef}>\n          {/* Render items twice for infinite scroll */}\n          {[...portfolioItems, ...portfolioItems].map((item, index) => (\n            <div key={index} className=\"portfolio-item\">\n              <a href={item.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                <img src={item.image} alt={item.alt} />\n                <p>{item.title}</p>\n              </a>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,gBAAgB,GAAGL,MAAM,CAAC,IAAI,CAAC;EAErCD,SAAS,CAAC,MAAM;IACd,MAAMO,aAAa,GAAGD,gBAAgB,CAACE,OAAO;IAC9C,IAAI,CAACD,aAAa,EAAE;IAEpB,IAAIE,UAAU,GAAG,KAAK;IACtB,IAAIC,MAAM;IACV,IAAIC,eAAe;IACnB,IAAIC,kBAAkB;IACtB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;;IAEvC;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIL,kBAAkB,EAAEM,aAAa,CAACN,kBAAkB,CAAC;MACzDA,kBAAkB,GAAGO,WAAW,CAAC,MAAM;QACrC;QACAZ,aAAa,CAACa,UAAU,IAAIP,WAAW;QACvC,MAAMQ,UAAU,GAAGd,aAAa,CAACe,WAAW,GAAG,CAAC;QAChD,IAAIf,aAAa,CAACa,UAAU,IAAIC,UAAU,EAAE;UAC1Cd,aAAa,CAACa,UAAU,GAAG,CAAC;QAC9B;MACF,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIX,kBAAkB,EAAE;QACtBM,aAAa,CAACN,kBAAkB,CAAC;QACjCA,kBAAkB,GAAG,IAAI;MAC3B;IACF,CAAC;;IAED;IACA,MAAMY,eAAe,GAAIC,CAAC,IAAK;MAC7BhB,UAAU,GAAG,IAAI;MACjBF,aAAa,CAACmB,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvCjB,MAAM,GAAGe,CAAC,CAACG,KAAK;MAChBjB,eAAe,GAAGJ,aAAa,CAACa,UAAU;IAC5C,CAAC;IAED,MAAMS,eAAe,GAAIJ,CAAC,IAAK;MAC7B,IAAI,CAAChB,UAAU,EAAE;MACjB,MAAMqB,CAAC,GAAGL,CAAC,CAACG,KAAK;MACjB,MAAMG,IAAI,GAAG,CAACD,CAAC,GAAGpB,MAAM,IAAI,GAAG;MAC/BH,aAAa,CAACa,UAAU,GAAGT,eAAe,GAAGoB,IAAI;MAEjD,MAAMV,UAAU,GAAGd,aAAa,CAACe,WAAW,GAAG,CAAC;MAChD,IAAIf,aAAa,CAACa,UAAU,IAAIC,UAAU,EAAE;QAC1Cd,aAAa,CAACa,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIb,aAAa,CAACa,UAAU,GAAG,CAAC,EAAE;QACvCb,aAAa,CAACa,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;IAED,MAAMW,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIvB,UAAU,EAAE;QACdA,UAAU,GAAG,KAAK;QAClBF,aAAa,CAACmB,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC;MAC5C;IACF,CAAC;IAED,MAAMC,WAAW,GAAIT,CAAC,IAAK;MACzBA,CAAC,CAACU,cAAc,CAAC,CAAC;MAClB,MAAMC,UAAU,GAAGX,CAAC,CAACY,MAAM;MAC3B,MAAMC,YAAY,GAAGF,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;MAC9C7B,aAAa,CAACa,UAAU,IAAIkB,YAAY;MAExC,MAAMjB,UAAU,GAAGd,aAAa,CAACe,WAAW,GAAG,CAAC;MAChD,IAAIf,aAAa,CAACa,UAAU,IAAIC,UAAU,EAAE;QAC1Cd,aAAa,CAACa,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIb,aAAa,CAACa,UAAU,GAAG,CAAC,EAAE;QACvCb,aAAa,CAACa,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;;IAED;IACA,MAAMkB,gBAAgB,GAAId,CAAC,IAAK;MAC9BhB,UAAU,GAAG,IAAI;MACjBF,aAAa,CAACmB,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvCjB,MAAM,GAAGe,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACZ,KAAK;MAC3BjB,eAAe,GAAGJ,aAAa,CAACa,UAAU;IAC5C,CAAC;IAED,MAAMqB,eAAe,GAAIhB,CAAC,IAAK;MAC7B,IAAI,CAAChB,UAAU,EAAE;MACjB;MACA,IAAIK,QAAQ,EAAE;QACZ;QACA;MACF;;MAEA;MACA,MAAMgB,CAAC,GAAGL,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACZ,KAAK;MAC5B,MAAMG,IAAI,GAAG,CAACD,CAAC,GAAGpB,MAAM,IAAI,GAAG;MAC/BH,aAAa,CAACa,UAAU,GAAGT,eAAe,GAAGoB,IAAI;MAEjD,MAAMV,UAAU,GAAGd,aAAa,CAACe,WAAW,GAAG,CAAC;MAChD,IAAIf,aAAa,CAACa,UAAU,IAAIC,UAAU,EAAE;QAC1Cd,aAAa,CAACa,UAAU,GAAG,CAAC;MAC9B,CAAC,MAAM,IAAIb,aAAa,CAACa,UAAU,GAAG,CAAC,EAAE;QACvCb,aAAa,CAACa,UAAU,GAAGC,UAAU,GAAG,CAAC;MAC3C;IACF,CAAC;IAED,MAAMqB,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIjC,UAAU,EAAE;QACdkC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1BpC,UAAU,GAAG,KAAK;QAClBqC,UAAU,GAAG,KAAK;QAClBvC,aAAa,CAACmB,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC;MAC5C;IACF,CAAC;;IAED;IACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACzBjC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;IACrC,CAAC;;IAED;IACA,MAAMgC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIlC,QAAQ,EAAE;QACZ,MAAMO,UAAU,GAAGd,aAAa,CAACe,WAAW,GAAG,CAAC;QAChD,IAAIf,aAAa,CAACa,UAAU,IAAIC,UAAU,EAAE;UAC1Cd,aAAa,CAACa,UAAU,GAAG,CAAC;QAC9B,CAAC,MAAM,IAAIb,aAAa,CAACa,UAAU,GAAG,CAAC,EAAE;UACvCb,aAAa,CAACa,UAAU,GAAGC,UAAU,GAAG,CAAC;QAC3C;MACF;IACF,CAAC;;IAED;IACAd,aAAa,CAAC0C,gBAAgB,CAAC,YAAY,EAAEC,gBAAgB,CAAC;IAC9D3C,aAAa,CAAC0C,gBAAgB,CAAC,YAAY,EAAEE,gBAAgB,CAAC;IAC9D5C,aAAa,CAAC0C,gBAAgB,CAAC,WAAW,EAAEzB,eAAe,CAAC;IAC5DjB,aAAa,CAAC0C,gBAAgB,CAAC,WAAW,EAAEpB,eAAe,CAAC;IAC5DuB,QAAQ,CAACH,gBAAgB,CAAC,SAAS,EAAEjB,aAAa,CAAC;IACnDzB,aAAa,CAAC0C,gBAAgB,CAAC,OAAO,EAAEf,WAAW,EAAE;MAAEmB,OAAO,EAAE;IAAM,CAAC,CAAC;;IAExE;IACA9C,aAAa,CAAC0C,gBAAgB,CAAC,YAAY,EAAEV,gBAAgB,EAAE;MAAEc,OAAO,EAAE;IAAK,CAAC,CAAC;IACjF9C,aAAa,CAAC0C,gBAAgB,CAAC,WAAW,EAAER,eAAe,EAAE;MAAEY,OAAO,EAAE;IAAK,CAAC,CAAC;IAC/E9C,aAAa,CAAC0C,gBAAgB,CAAC,UAAU,EAAEP,cAAc,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;;IAE7E;IACAtC,MAAM,CAACkC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/CxC,aAAa,CAAC0C,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;;IAEtD;IACA/B,eAAe,CAAC,CAAC;;IAEjB;IACA,OAAO,MAAM;MACXM,cAAc,CAAC,CAAC;MAChBhB,aAAa,CAAC+C,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;MACjE3C,aAAa,CAAC+C,mBAAmB,CAAC,YAAY,EAAEH,gBAAgB,CAAC;MACjE5C,aAAa,CAAC+C,mBAAmB,CAAC,WAAW,EAAE9B,eAAe,CAAC;MAC/DjB,aAAa,CAAC+C,mBAAmB,CAAC,WAAW,EAAEzB,eAAe,CAAC;MAC/DuB,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEtB,aAAa,CAAC;MACtDzB,aAAa,CAAC+C,mBAAmB,CAAC,OAAO,EAAEpB,WAAW,CAAC;;MAEvD;MACA3B,aAAa,CAAC+C,mBAAmB,CAAC,YAAY,EAAEf,gBAAgB,CAAC;MACjEhC,aAAa,CAAC+C,mBAAmB,CAAC,WAAW,EAAEb,eAAe,CAAC;MAC/DlC,aAAa,CAAC+C,mBAAmB,CAAC,UAAU,EAAEZ,cAAc,CAAC;;MAE7D;MACA3B,MAAM,CAACuC,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClDxC,aAAa,CAAC+C,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,cAAc,GAAG,CACrB;IACEC,IAAI,EAAE,wCAAwC;IAC9CC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,YAAY;IACnBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,sBAAsB;IAC7BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACExD,OAAA;IAASyD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAC5B1D,OAAA;MAAA0D,QAAA,GAAI,cAAY,eAAA1D,OAAA;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3B9D,OAAA;MAAQyD,SAAS,EAAC,iBAAiB;MAACM,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE;MAAAP,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC/G9D,OAAA;MAAKyD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjC1D,OAAA;QAAKyD,SAAS,EAAC,gBAAgB;QAACS,GAAG,EAAE/D,gBAAiB;QAAAuD,QAAA,EAEnD,CAAC,GAAGN,cAAc,EAAE,GAAGA,cAAc,CAAC,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtDrE,OAAA;UAAiByD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eACzC1D,OAAA;YAAGqD,IAAI,EAAEe,IAAI,CAACf,IAAK;YAACiB,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAb,QAAA,gBAC3D1D,OAAA;cAAKwE,GAAG,EAAEJ,IAAI,CAACd,KAAM;cAACC,GAAG,EAAEa,IAAI,CAACb;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC9D,OAAA;cAAA0D,QAAA,EAAIU,IAAI,CAACZ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GAJIO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC5D,EAAA,CA7OID,SAAS;AAAAwE,EAAA,GAATxE,SAAS;AA+Of,eAAeA,SAAS;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}