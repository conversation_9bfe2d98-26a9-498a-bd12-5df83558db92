[{"C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js": "4", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js": "8", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js": "9", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js": "10", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js": "11", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js": "12", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js": "13", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js": "14", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js": "15", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js": "16", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js": "17", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js": "18", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ScrollToTop.js": "19"}, {"size": 535, "mtime": 1750766591000, "results": "20", "hashOfConfig": "21"}, {"size": 552, "mtime": 1751021411575, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1750766591000, "results": "23", "hashOfConfig": "21"}, {"size": 7265, "mtime": 1751021439578, "results": "24", "hashOfConfig": "21"}, {"size": 710, "mtime": 1750766591000, "results": "25", "hashOfConfig": "21"}, {"size": 2291, "mtime": 1751024444504, "results": "26", "hashOfConfig": "21"}, {"size": 378, "mtime": 1750766591000, "results": "27", "hashOfConfig": "21"}, {"size": 6543, "mtime": 1750766591000, "results": "28", "hashOfConfig": "21"}, {"size": 1770, "mtime": 1750766591000, "results": "29", "hashOfConfig": "21"}, {"size": 3068, "mtime": 1750766591000, "results": "30", "hashOfConfig": "21"}, {"size": 786, "mtime": 1751022541809, "results": "31", "hashOfConfig": "21"}, {"size": 843, "mtime": 1750766591000, "results": "32", "hashOfConfig": "21"}, {"size": 1765, "mtime": 1750766591000, "results": "33", "hashOfConfig": "21"}, {"size": 1806, "mtime": 1751023563768, "results": "34", "hashOfConfig": "21"}, {"size": 750, "mtime": 1750766591000, "results": "35", "hashOfConfig": "21"}, {"size": 1289, "mtime": 1750766591000, "results": "36", "hashOfConfig": "21"}, {"size": 259, "mtime": 1750766591000, "results": "37", "hashOfConfig": "21"}, {"size": 13908, "mtime": 1750793313528, "results": "38", "hashOfConfig": "21"}, {"size": 540, "mtime": 1751021452555, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fii283", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js", ["97"], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js", ["98", "99"], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ScrollToTop.js", [], [], {"ruleId": "100", "severity": 1, "message": "101", "line": 1, "column": 27, "nodeType": "102", "messageId": "103", "endLine": 1, "endColumn": 34}, {"ruleId": "100", "severity": 1, "message": "104", "line": 2, "column": 10, "nodeType": "102", "messageId": "103", "endLine": 2, "endColumn": 14}, {"ruleId": "100", "severity": 1, "message": "105", "line": 8, "column": 10, "nodeType": "102", "messageId": "103", "endLine": 8, "endColumn": 20}, "no-unused-vars", "'useMemo' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used.", "'clickCount' is assigned a value but never used."]