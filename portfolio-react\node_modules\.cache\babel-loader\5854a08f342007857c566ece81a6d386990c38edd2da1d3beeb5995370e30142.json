{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MODAL_TIMEOUT = 1200; // ms to reset click count\n\nconst Header = () => {\n  _s();\n  const [showAdminModal, setShowAdminModal] = useState(false);\n  const [clickCount, setClickCount] = useState(0);\n  const clickTimer = useRef(null);\n  const handleLogoClick = e => {\n    e.preventDefault();\n    setClickCount(prev => {\n      const newCount = prev + 1;\n      if (clickTimer.current) clearTimeout(clickTimer.current);\n      if (newCount >= 5) {\n        setShowAdminModal(true);\n        setTimeout(() => setClickCount(0), 100); // reset after modal\n        return 0;\n      } else {\n        clickTimer.current = setTimeout(() => setClickCount(0), MODAL_TIMEOUT);\n        return newCount;\n      }\n    });\n    // No reload or navigation on single click\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/\",\n        onClick: handleLogoClick,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo.PNG\",\n          alt: \"Logo\",\n          className: \"logo-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\",\n      className: \"cv-button\",\n      target: \"_blank\",\n      rel: \"noopener noreferrer\",\n      children: \"Get CV\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), showAdminModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-modal-overlay\",\n      onClick: () => setShowAdminModal(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"admin-modal-close\",\n          onClick: () => setShowAdminModal(false),\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"admin-modal-title\",\n          children: \"Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"admin-modal-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"admin-email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"admin-email\",\n            name: \"email\",\n            autoComplete: \"username\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"admin-password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"admin-password\",\n            name: \"password\",\n            autoComplete: \"current-password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"admin-modal-submit\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"DjzQhCJxnJSYSiZqrcXWvcFHuSw=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "jsxDEV", "_jsxDEV", "MODAL_TIMEOUT", "Header", "_s", "showAdminModal", "setShowAdminModal", "clickCount", "setClickCount", "clickTimer", "handleLogoClick", "e", "preventDefault", "prev", "newCount", "current", "clearTimeout", "setTimeout", "children", "className", "href", "onClick", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "target", "rel", "stopPropagation", "htmlFor", "type", "id", "name", "autoComplete", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Header.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst MODAL_TIMEOUT = 1200; // ms to reset click count\n\nconst Header = () => {\n  const [showAdminModal, setShowAdminModal] = useState(false);\n  const [clickCount, setClickCount] = useState(0);\n  const clickTimer = useRef(null);\n\n  const handleLogoClick = (e) => {\n    e.preventDefault();\n    setClickCount(prev => {\n      const newCount = prev + 1;\n      if (clickTimer.current) clearTimeout(clickTimer.current);\n      if (newCount >= 5) {\n        setShowAdminModal(true);\n        setTimeout(() => setClickCount(0), 100); // reset after modal\n        return 0;\n      } else {\n        clickTimer.current = setTimeout(() => setClickCount(0), MODAL_TIMEOUT);\n        return newCount;\n      }\n    });\n    // No reload or navigation on single click\n  };\n\n  return (\n    <header>\n      <div className=\"logo\">\n        <a href=\"/\" onClick={handleLogoClick}>\n          <img src=\"/logo.PNG\" alt=\"Logo\" className=\"logo-img\" />\n        </a>\n      </div>\n      <a \n        href=\"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\" \n        className=\"cv-button\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        Get CV\n      </a>\n      {showAdminModal && (\n        <div className=\"admin-modal-overlay\" onClick={() => setShowAdminModal(false)}>\n          <div className=\"admin-modal\" onClick={e => e.stopPropagation()}>\n            <button className=\"admin-modal-close\" onClick={() => setShowAdminModal(false)}>&times;</button>\n            <h2 className=\"admin-modal-title\">Admin Login</h2>\n            <form className=\"admin-modal-form\">\n              <label htmlFor=\"admin-email\">Email</label>\n              <input type=\"email\" id=\"admin-email\" name=\"email\" autoComplete=\"username\" required />\n              <label htmlFor=\"admin-password\">Password</label>\n              <input type=\"password\" id=\"admin-password\" name=\"password\" autoComplete=\"current-password\" required />\n              <button type=\"submit\" className=\"admin-modal-submit\">Login</button>\n            </form>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAG,IAAI,CAAC,CAAC;;AAE5B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMY,UAAU,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMY,eAAe,GAAIC,CAAC,IAAK;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBJ,aAAa,CAACK,IAAI,IAAI;MACpB,MAAMC,QAAQ,GAAGD,IAAI,GAAG,CAAC;MACzB,IAAIJ,UAAU,CAACM,OAAO,EAAEC,YAAY,CAACP,UAAU,CAACM,OAAO,CAAC;MACxD,IAAID,QAAQ,IAAI,CAAC,EAAE;QACjBR,iBAAiB,CAAC,IAAI,CAAC;QACvBW,UAAU,CAAC,MAAMT,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC;MACV,CAAC,MAAM;QACLC,UAAU,CAACM,OAAO,GAAGE,UAAU,CAAC,MAAMT,aAAa,CAAC,CAAC,CAAC,EAAEN,aAAa,CAAC;QACtE,OAAOY,QAAQ;MACjB;IACF,CAAC,CAAC;IACF;EACF,CAAC;EAED,oBACEb,OAAA;IAAAiB,QAAA,gBACEjB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBjB,OAAA;QAAGmB,IAAI,EAAC,GAAG;QAACC,OAAO,EAAEX,eAAgB;QAAAQ,QAAA,eACnCjB,OAAA;UAAKqB,GAAG,EAAC,WAAW;UAACC,GAAG,EAAC,MAAM;UAACJ,SAAS,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACN1B,OAAA;MACEmB,IAAI,EAAC,iLAAiL;MACtLD,SAAS,EAAC,WAAW;MACrBS,MAAM,EAAC,QAAQ;MACfC,GAAG,EAAC,qBAAqB;MAAAX,QAAA,EAC1B;IAED;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EACHtB,cAAc,iBACbJ,OAAA;MAAKkB,SAAS,EAAC,qBAAqB;MAACE,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAAC,KAAK,CAAE;MAAAY,QAAA,eAC3EjB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAACE,OAAO,EAAEV,CAAC,IAAIA,CAAC,CAACmB,eAAe,CAAC,CAAE;QAAAZ,QAAA,gBAC7DjB,OAAA;UAAQkB,SAAS,EAAC,mBAAmB;UAACE,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAAC,KAAK,CAAE;UAAAY,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/F1B,OAAA;UAAIkB,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAC;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD1B,OAAA;UAAMkB,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAChCjB,OAAA;YAAO8B,OAAO,EAAC,aAAa;YAAAb,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C1B,OAAA;YAAO+B,IAAI,EAAC,OAAO;YAACC,EAAE,EAAC,aAAa;YAACC,IAAI,EAAC,OAAO;YAACC,YAAY,EAAC,UAAU;YAACC,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrF1B,OAAA;YAAO8B,OAAO,EAAC,gBAAgB;YAAAb,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD1B,OAAA;YAAO+B,IAAI,EAAC,UAAU;YAACC,EAAE,EAAC,gBAAgB;YAACC,IAAI,EAAC,UAAU;YAACC,YAAY,EAAC,kBAAkB;YAACC,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtG1B,OAAA;YAAQ+B,IAAI,EAAC,QAAQ;YAACb,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACvB,EAAA,CAtDID,MAAM;AAAAkC,EAAA,GAANlC,MAAM;AAwDZ,eAAeA,MAAM;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}